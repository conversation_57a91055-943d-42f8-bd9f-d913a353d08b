#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script to verify that the use_auto_smooth fix is working
"""

import bpy
import sys
import os

def test_use_auto_smooth_fix():
    """Test if the use_auto_smooth fix is working"""
    
    print("Testing use_auto_smooth fix...")
    print(f"Blender version: {bpy.app.version}")
    
    # Create a test mesh
    bpy.ops.mesh.primitive_cube_add()
    test_obj = bpy.context.active_object
    test_mesh = test_obj.data
    
    # Check if use_auto_smooth exists
    has_auto_smooth = hasattr(test_mesh, 'use_auto_smooth')
    print(f"Mesh has use_auto_smooth: {has_auto_smooth}")
    
    # Test the setNormals method directly
    try:
        # Import the BlenderMod3Importer module
        addon_path = os.path.dirname(os.path.abspath(__file__))
        if addon_path not in sys.path:
            sys.path.insert(0, addon_path)
        
        from blender.BlenderMod3Importer import BlenderImporterAPI
        
        # Create some dummy normals
        normals = [(0, 0, 1) for _ in range(len(test_mesh.vertices))]
        
        # Test the setNormals method
        print("Testing setNormals method...")
        BlenderImporterAPI.setNormals(normals, test_mesh)
        print("✅ setNormals method executed successfully!")
        
        # Test the addSmoothByAngleModifier method if needed
        if not has_auto_smooth:
            print("Testing addSmoothByAngleModifier method...")
            BlenderImporterAPI.addSmoothByAngleModifier(test_obj)
            print("✅ addSmoothByAngleModifier method executed successfully!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing fix: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Clean up
        bpy.ops.object.delete()

def main():
    print("=" * 50)
    print("Blender 4.4.3 Compatibility Fix Verification")
    print("=" * 50)
    
    success = test_use_auto_smooth_fix()
    
    print("=" * 50)
    if success:
        print("✅ All tests passed! The fix is working correctly.")
    else:
        print("❌ Tests failed! There may still be compatibility issues.")
    print("=" * 50)

if __name__ == "__main__":
    main()
