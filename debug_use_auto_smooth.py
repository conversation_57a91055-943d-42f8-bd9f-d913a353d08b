#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug script to check use_auto_smooth usage in the codebase
"""

import os
import re

def search_use_auto_smooth(directory):
    """Search for use_auto_smooth usage in Python files"""
    results = []
    
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.py'):
                filepath = os.path.join(root, file)
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        for i, line in enumerate(lines, 1):
                            if 'use_auto_smooth' in line and not line.strip().startswith('#'):
                                # Check if this line has hasattr check
                                has_hasattr = False
                                # Check current line and a few lines before
                                for j in range(max(0, i-5), i):
                                    if j < len(lines) and 'hasattr' in lines[j] and 'use_auto_smooth' in lines[j]:
                                        has_hasattr = True
                                        break
                                
                                results.append({
                                    'file': filepath,
                                    'line': i,
                                    'content': line.strip(),
                                    'has_hasattr_check': has_hasattr
                                })
                except Exception as e:
                    print(f"Error reading {filepath}: {e}")
    
    return results

def main():
    # Search in current directory
    current_dir = os.getcwd()
    print(f"Searching for use_auto_smooth usage in: {current_dir}")
    print("=" * 60)
    
    results = search_use_auto_smooth(current_dir)
    
    if not results:
        print("No use_auto_smooth usage found!")
        return
    
    print(f"Found {len(results)} use_auto_smooth references:")
    print()
    
    problematic = []
    safe = []
    
    for result in results:
        rel_path = os.path.relpath(result['file'], current_dir)
        status = "✅ SAFE" if result['has_hasattr_check'] else "❌ PROBLEMATIC"
        
        print(f"{status} - {rel_path}:{result['line']}")
        print(f"    {result['content']}")
        print()
        
        if result['has_hasattr_check']:
            safe.append(result)
        else:
            problematic.append(result)
    
    print("=" * 60)
    print(f"Summary:")
    print(f"  Safe references (with hasattr check): {len(safe)}")
    print(f"  Problematic references (without hasattr check): {len(problematic)}")
    
    if problematic:
        print("\n❌ PROBLEMATIC REFERENCES THAT NEED FIXING:")
        for result in problematic:
            rel_path = os.path.relpath(result['file'], current_dir)
            print(f"  - {rel_path}:{result['line']} - {result['content']}")

if __name__ == "__main__":
    main()
