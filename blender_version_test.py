#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Blender 4.4.3 Compatibility Test for Mod3 Importer
Tests various API compatibility issues
"""

import bpy

def test_blender_compatibility():
    """Test Blender version compatibility for various APIs"""

    print(f"Blender version: {bpy.app.version}")
    print(f"Blender version string: {bpy.app.version_string}")
    print("=" * 50)

    # Create a test mesh
    bpy.ops.mesh.primitive_cube_add()
    test_obj = bpy.context.active_object
    test_mesh = test_obj.data

    # Test 1: use_auto_smooth compatibility
    print("1. Testing use_auto_smooth compatibility:")
    has_auto_smooth = hasattr(test_mesh, 'use_auto_smooth')
    print(f"   Has use_auto_smooth attribute: {has_auto_smooth}")

    if has_auto_smooth:
        print("   ✓ This Blender version supports use_auto_smooth (pre-4.1)")
        try:
            test_mesh.use_auto_smooth = True
            print("   ✓ Successfully set use_auto_smooth = True")
        except Exception as e:
            print(f"   ✗ Error setting use_auto_smooth: {e}")
    else:
        print("   ⚠ This Blender version does NOT support use_auto_smooth (4.1+)")
        print("   → Need to use Smooth by Angle modifier instead")

        # Test if we can add a Smooth by Angle modifier
        try:
            bpy.ops.object.shade_smooth_by_angle()
            print("   ✓ Successfully added Smooth by Angle modifier using operator")
        except Exception as e:
            print(f"   ⚠ Error adding Smooth by Angle modifier: {e}")

            # Try manual modifier creation
            try:
                modifier = test_obj.modifiers.new(name="Smooth by Angle", type='NODES')
                print("   ✓ Successfully created geometry nodes modifier")
            except Exception as e2:
                print(f"   ✗ Error creating geometry nodes modifier: {e2}")

    # Test 2: vertex_colors vs color_attributes
    print("\n2. Testing vertex colors compatibility:")
    has_vertex_colors = hasattr(test_mesh, 'vertex_colors')
    has_color_attributes = hasattr(test_mesh, 'color_attributes')
    print(f"   Has vertex_colors attribute: {has_vertex_colors}")
    print(f"   Has color_attributes attribute: {has_color_attributes}")

    if has_color_attributes:
        try:
            color_attr = test_mesh.color_attributes.new(name="TestColor", type='BYTE_COLOR', domain='CORNER')
            print("   ✓ Successfully created color attribute (Blender 4.2+ method)")
            test_mesh.color_attributes.remove(color_attr)
        except Exception as e:
            print(f"   ✗ Error creating color attribute: {e}")

    if has_vertex_colors:
        try:
            vcol = test_mesh.vertex_colors.new(name="TestVertexColor")
            print("   ✓ Successfully created vertex color layer (legacy method)")
            test_mesh.vertex_colors.remove(vcol)
        except Exception as e:
            print(f"   ✗ Error creating vertex color layer: {e}")

    # Test 3: Object selection API
    print("\n3. Testing object selection API:")
    try:
        test_obj.select_set(True)
        print("   ✓ obj.select_set() works (Blender 2.8+ method)")
    except Exception as e:
        print(f"   ✗ Error with obj.select_set(): {e}")

    # Test 4: Active object API
    print("\n4. Testing active object API:")
    try:
        bpy.context.view_layer.objects.active = test_obj
        print("   ✓ bpy.context.view_layer.objects.active works (Blender 2.8+ method)")
    except Exception as e:
        print(f"   ✗ Error with view_layer.objects.active: {e}")

    # Test 5: Object linking API
    print("\n5. Testing object linking API:")
    try:
        # Create a test object
        test_mesh2 = bpy.data.meshes.new("TestMesh2")
        test_obj2 = bpy.data.objects.new("TestObj2", test_mesh2)
        bpy.context.collection.objects.link(test_obj2)
        print("   ✓ bpy.context.collection.objects.link() works (Blender 2.8+ method)")
        # Clean up
        bpy.data.objects.remove(test_obj2)
        bpy.data.meshes.remove(test_mesh2)
    except Exception as e:
        print(f"   ✗ Error with collection.objects.link(): {e}")

    # Test 6: UV layers
    print("\n6. Testing UV layers API:")
    try:
        uv_layer = test_mesh.uv_layers.new(name="TestUV")
        print("   ✓ mesh.uv_layers.new() works")
        test_mesh.uv_layers.remove(uv_layer)
    except Exception as e:
        print(f"   ✗ Error with uv_layers: {e}")

    # Clean up
    bpy.ops.object.delete()

    print("\n" + "=" * 50)
    print("Compatibility test completed!")

    return {
        'has_auto_smooth': has_auto_smooth,
        'has_vertex_colors': has_vertex_colors,
        'has_color_attributes': has_color_attributes
    }

if __name__ == "__main__":
    test_blender_compatibility()
