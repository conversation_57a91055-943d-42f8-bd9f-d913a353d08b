#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Blender Version Compatibility Test for Mod3 Importer
Tests whether the current Blender version supports use_auto_smooth
"""

import bpy

def test_blender_compatibility():
    """Test Blender version compatibility for use_auto_smooth"""
    
    print(f"Blender version: {bpy.app.version}")
    print(f"Blender version string: {bpy.app.version_string}")
    
    # Create a test mesh
    bpy.ops.mesh.primitive_cube_add()
    test_obj = bpy.context.active_object
    test_mesh = test_obj.data
    
    # Test if use_auto_smooth exists
    has_auto_smooth = hasattr(test_mesh, 'use_auto_smooth')
    print(f"Has use_auto_smooth attribute: {has_auto_smooth}")
    
    if has_auto_smooth:
        print("This Blender version supports use_auto_smooth (pre-4.1)")
        try:
            test_mesh.use_auto_smooth = True
            print("Successfully set use_auto_smooth = True")
        except Exception as e:
            print(f"Error setting use_auto_smooth: {e}")
    else:
        print("This Blender version does NOT support use_auto_smooth (4.1+)")
        print("Need to use Smooth by Angle modifier instead")
        
        # Test if we can add a Smooth by Angle modifier
        try:
            bpy.ops.object.shade_smooth_by_angle()
            print("Successfully added Smooth by Angle modifier using operator")
        except Exception as e:
            print(f"Error adding Smooth by Angle modifier: {e}")
            
            # Try manual modifier creation
            try:
                modifier = test_obj.modifiers.new(name="Smooth by Angle", type='NODES')
                print("Successfully created geometry nodes modifier")
            except Exception as e2:
                print(f"Error creating geometry nodes modifier: {e2}")
    
    # Clean up
    bpy.ops.object.delete()
    
    return has_auto_smooth

if __name__ == "__main__":
    test_blender_compatibility()
