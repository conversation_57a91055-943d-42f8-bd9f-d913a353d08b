# 🚨 快速修复指南

## 问题描述
如果您遇到以下错误：
```
RuntimeError: Error: No module named 'Mod3-MHW-Importer-1'
```

## 解决方案

### 步骤1: 重命名文件夹
**将插件文件夹从 `Mod3-MHW-Importer-1.5.0` 重命名为 `Mod3_MHW_Importer`**

- ❌ 错误名称: `Mod3-MHW-Importer-1.5.0` (包含连字符)
- ✅ 正确名称: `Mod3_MHW_Importer` (使用下划线)

### 步骤2: 重新安装插件

1. **卸载旧插件**
   - 在Blender中打开 Edit > Preferences > Add-ons
   - 搜索 "MHW" 或 "Mod3"
   - 如果找到插件，点击删除按钮

2. **安装重命名后的插件**
   - 点击 "Install..." 按钮
   - 选择重命名后的 `Mod3_MHW_Importer` 文件夹
   - 启用插件

### 步骤3: 验证安装
检查以下菜单项是否出现：
- File > Import > MHW MOD3 (.mod3)
- File > Export > MHW MOD3 (.mod3)

## 为什么需要重命名？

Python模块名称规则：
- ✅ 允许: 字母、数字、下划线 (`_`)
- ❌ 不允许: 连字符 (`-`)、空格、特殊字符

Blender将插件文件夹名作为Python模块名，因此必须遵循Python命名规则。

## 如果问题仍然存在

1. **完全重启Blender**
2. **检查文件夹结构**
   ```
   Mod3_MHW_Importer/
   ├── __init__.py
   ├── operators/
   ├── blender/
   ├── mod3/
   └── ...
   ```
3. **查看控制台错误信息**
   - Window > Toggle System Console

## 联系支持
如果问题仍未解决，请提供：
- Blender版本
- 操作系统
- 完整的错误信息
- 文件夹结构截图
