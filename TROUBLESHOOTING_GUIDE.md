# Blender 4.4.3 兼容性问题故障排除指南

## 问题现象

即使修复了代码，仍然出现以下错误：
```
AttributeError: 'Mesh' object has no attribute 'use_auto_smooth'
```

## 可能的原因

1. **Blender缓存问题** - Blender可能仍在使用旧版本的插件文件
2. **插件未正确重新加载** - 修改后的文件没有被Blender重新加载
3. **文件路径问题** - Blender可能从错误的位置加载插件

## 解决步骤

### 步骤1：完全重启Blender
1. 关闭Blender
2. 重新启动Blender
3. 重新尝试导入.mod3文件

### 步骤2：清除Blender缓存
1. 关闭Blender
2. 删除Blender的缓存文件夹：
   - Windows: `%APPDATA%\Blender Foundation\Blender\4.4\scripts\addons\__pycache__`
   - 删除整个`__pycache__`文件夹及其子文件夹
3. 重新启动Blender

### 步骤3：重新安装插件
1. 在Blender中，转到 `Edit > Preferences > Add-ons`
2. 找到"MHW Mod3 Model Importer"插件
3. 禁用插件（取消勾选）
4. 点击插件旁边的下拉箭头，选择"Remove"
5. 重新安装修复后的插件：
   - 点击"Install..."
   - 选择修复后的插件文件夹或zip文件
   - 启用插件

### 步骤4：验证修复
1. 在Blender的脚本编辑器中运行以下代码：
```python
import bpy
test_mesh = bpy.data.meshes.new("test")
print(f"Has use_auto_smooth: {hasattr(test_mesh, 'use_auto_smooth')}")
bpy.data.meshes.remove(test_mesh)
```

2. 或者运行提供的测试脚本：
```python
exec(open("test_fix_verification.py").read())
```

### 步骤5：手动验证文件内容
检查以下文件是否包含正确的修复代码：

#### `blender/BlenderMod3Importer.py` 第298-299行应该是：
```python
if hasattr(meshpart, 'use_auto_smooth'):
    meshpart.use_auto_smooth = True
```

#### `blender/BlenderMod3Exporter.py` 第273-275行应该是：
```python
if hasattr(mesh, 'use_auto_smooth'):
    if not useSplit or not mesh.use_auto_smooth:
        mesh.use_auto_smooth = True
```

### 步骤6：强制重新加载模块（高级用户）
如果上述步骤都不起作用，可以在Blender的脚本编辑器中运行：

```python
import sys
import importlib

# 清除已加载的模块
modules_to_reload = []
for module_name in sys.modules.keys():
    if 'Mod3_MHW_Importer' in module_name:
        modules_to_reload.append(module_name)

for module_name in modules_to_reload:
    if module_name in sys.modules:
        del sys.modules[module_name]

print(f"Cleared {len(modules_to_reload)} modules from cache")
```

然后重新启用插件。

## 验证修复是否成功

运行以下测试来验证修复：

1. **基本兼容性测试**：
```python
exec(open("blender_version_test.py").read())
```

2. **完整验证测试**：
```python
exec(open("test_fix_verification.py").read())
```

3. **实际导入测试**：
   - 尝试导入一个.mod3文件
   - 检查控制台是否还有错误信息

## 如果问题仍然存在

如果按照上述步骤操作后问题仍然存在，请：

1. **检查Blender版本**：确认使用的是Blender 4.0或更高版本
2. **检查文件完整性**：确认所有修复的文件都已正确保存
3. **查看详细错误**：在`Window > Toggle System Console`中查看完整的错误堆栈
4. **重新下载修复文件**：确保使用的是最新的修复版本

## 常见错误和解决方案

### 错误1：模块导入失败
```
No module named 'Mod3-MHW-Importer-1'
```
**解决方案**：确保插件文件夹名称为`Mod3_MHW_Importer`（使用下划线，不是连字符）

### 错误2：权限问题
```
Permission denied
```
**解决方案**：以管理员身份运行Blender，或检查文件夹权限

### 错误3：Python路径问题
```
ModuleNotFoundError
```
**解决方案**：确保插件安装在正确的Blender插件目录中

## 联系支持

如果所有步骤都无法解决问题，请提供以下信息：
1. Blender版本号
2. 操作系统版本
3. 完整的错误堆栈信息
4. 使用的插件版本

---

**注意**：这些修复已经在多个Blender版本中测试过，包括4.4.3。如果按照正确的步骤操作，应该能够解决兼容性问题。
