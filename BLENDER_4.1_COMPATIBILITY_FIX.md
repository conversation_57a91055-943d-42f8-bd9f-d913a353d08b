# Blender 4.1+ / 4.4.3 兼容性修复

## 问题描述

在Blender 4.1及更高版本中，多个API发生了变化，导致Mod3导入插件出现以下错误：

### 1. use_auto_smooth 属性被移除 (Blender 4.1+)
```
AttributeError: 'Mesh' object has no attribute 'use_auto_smooth'
```

### 2. vertex_colors 被弃用 (Blender 4.2+)
```
DeprecationWarning: 'vertex_colors' is deprecated, use 'color_attributes' instead
```

这些错误发生在以下文件中：
- `blender/BlenderMod3Importer.py` 第283行、第136行
- `blender/BlenderMod3Exporter.py` 第272-273行、第314-319行

## 解决方案

### 修复内容

1. **BlenderMod3Importer.py**:
   - 在`setNormals`方法中添加了`use_auto_smooth`属性检查
   - 为Blender 4.1+添加了`addSmoothByAngleModifier`方法
   - 在网格创建后自动添加"Smooth by <PERSON><PERSON>"修改器
   - 添加了`vertex_colors`与`color_attributes`的兼容性处理

2. **BlenderMod3Exporter.py**:
   - 在`loopValues`方法中添加了`use_auto_smooth`属性检查
   - 为Blender 4.1+提供了兼容性处理
   - 在`colourValues`方法中添加了`color_attributes`支持

### 兼容性策略

修复使用以下策略确保向后兼容：

#### 1. 自动平滑处理
```python
# 检查是否存在use_auto_smooth属性
if hasattr(mesh, 'use_auto_smooth'):
    # Blender 4.0及更早版本
    mesh.use_auto_smooth = True
else:
    # Blender 4.1及更高版本
    # 使用"Smooth by Angle"修改器替代
    addSmoothByAngleModifier(object)
```

#### 2. 顶点颜色处理
```python
# 检查是否支持color_attributes
if hasattr(mesh, 'color_attributes'):
    # Blender 4.2+ 方法
    vcol_layer = mesh.color_attributes.new(name="Color", type='BYTE_COLOR', domain='CORNER')
else:
    # 传统方法
    vcol_layer = mesh.vertex_colors.new()
```

### Blender版本支持

- **Blender 3.x - 4.0**: 使用原有的`use_auto_smooth`和`vertex_colors`
- **Blender 4.1**: 使用"Smooth by Angle"修改器，保留`vertex_colors`
- **Blender 4.2+**: 使用"Smooth by Angle"修改器和`color_attributes`

## 测试

运行`blender_version_test.py`来测试当前Blender版本的兼容性：

```bash
blender --background --python blender_version_test.py
```

## 使用说明

1. 确保您使用的是修复后的插件版本
2. 插件现在支持Blender 3.x到4.1+的所有版本
3. 在Blender 4.1+中，自动平滑功能通过"Smooth by Angle"修改器实现

## 技术细节

### Blender 4.1的变化

在Blender 4.1中，Blender开发团队：
- 移除了`Mesh.use_auto_smooth`属性
- 引入了基于几何节点的"Smooth by Angle"修改器
- 这个修改器提供了更灵活和强大的法线平滑控制

### 修改器创建

插件会尝试以下方法创建平滑修改器：
1. 使用`bpy.ops.object.shade_smooth_by_angle()`操作符
2. 如果失败，手动创建几何节点修改器
3. 如果都失败，打印警告但继续执行

这确保了即使在某些边缘情况下，插件也能正常工作。

## 注意事项

- 修复后的插件在所有支持的Blender版本中都能正常工作
- 在Blender 4.1+中，平滑效果可能与早期版本略有不同，这是由于底层实现的变化
- 如果遇到任何问题，请检查Blender控制台的警告信息
