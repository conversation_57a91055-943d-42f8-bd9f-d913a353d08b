maptypeTranslation = {
     977442: 'SSAddNormal__disclosure',
     772180: 'SSAddNormalMask__disclosure',
     552381: 'SSAlbedo__disclosure',
     660359: 'SSAlbedoBlend__disclosure',
     823585: 'SSAlbedoBlendMap__disclosure',
     477313: 'SSAlpha',
     824615: 'SSAlpha__disclosure',
     108505: 'SSAtmosphereDepth',
     331765: 'SSAtmosphereScatter',
     401245: 'SSBase',
     655555: 'SSBlend',
     364198: 'SSBorderLinear',
     13386: 'SSBorderPoint',
     73134: 'SSClampLinear',
     880550: 'SSClampPoint',
     99178: 'SSColorDiv__disclosure',
     365488: 'SSColorGrading',
     666808: 'SSColorMask__disclosure',
     807044: 'SSCube',
     186869: 'SSCube__disclosure',
     364932: 'SSCubeToIRM',
     976376: 'SSDecalCube',
     33328: 'SSDecalFlow',
     290178: 'SSDepth',
     960011: 'SSDetail__disclosure',
     722641: 'SSDetailEmissive__disclosure',
     238852: 'SSDetailNormal__disclosure',
     690954: 'SSDevelop',
     349251: 'SSDisplacement__disclosure',
     857864: 'SSEmissive__disclosure',
     1033470: 'SSFaceMap__disclosure',
     252945: 'SSFilter',
     400715: 'SSFilterBlend',
     716553: 'SSFlow__disclosure',
     356010: 'SSFogLinear',
     506091: 'SSFogWrapLinear',
     578823: 'SSFur__disclosure',
     445440: 'SSFurNormal__disclosure',
     1535: 'SSFurVelocity__disclosure',
     847471: 'SSFx__disclosure',
     34524: 'SSGeneric__disclosure',
     1038037: 'SSGUI',
     605119: 'SSLinear',
     681695: 'SSLR_Classify',
     454127: 'SSLR_Classify_FULL_RES',
     531448: 'SSLR_ClearRay',
     198435: 'SSLR_Resolve',
     863213: 'SSLR_Trace_Accurate',
     545043: 'SSLR_Trace_Approximate',
     88615: 'SSLR_Trace_Approximate_FULL_RES',
     482863: 'SSLR_Trace_Clear',
     1038880: 'SSMaterialBlend__disclosure',
     680683: 'SSNormal__disclosure',
     870893: 'SSOpacity__disclosure',
     605045: 'SSP2O',
     742175: 'SSPaint__disclosure',
     125033: 'SSPanoramaMap__disclosure',
     261914: 'SSPattern__disclosure',
     164559: 'SSPoint',
     939595: 'SSPrimAlbedo',
     33073: 'SSPrimAlphaMask',
     641345: 'SSPrimCube',
     802198: 'SSPrimNormal',
     83195: 'SSPrimOcclusionMap',
     55711: 'SSPrimSceneTex',
     576184: 'SSPrimSceneTexDownSample',
     657404: 'SSPrimTubeLightTexture',
     714908: 'SSPrimTurbulence',
     243593: 'SSProjectionLight',
     988234: 'SSRadialFilterClampLinear',
     777688: 'SSRefraction',
     422663: 'SSRefractionMask__disclosure',
     188801: 'SSRMT__disclosure',
     65825: 'SSShadow',
     25196: 'SSShadowBlur',
     422413: 'SSShadowBlur_NO_SSAO',
     392544: 'SSShadowBlurFull',
     1037497: 'SSShadowBlurFull_NO_SSAO',
     413887: 'SSShadowUpsampling',
     993022: 'SSShadowUpsampling_NO_SSAO',
     23557: 'SSSHDiffuse',
     386079: 'SSSkin__disclosure',
     849576: 'SSSky',
     942729: 'SSSkyBase',
     183923: 'SSSkyBlend',
     240015: 'SSSkyCloud',
     451308: 'SSSkyCurve',
     244469: 'SSSkyStarrySky',
     959887: 'SSSkySun',
     826259: 'SSSkySunBloom',
     655578: 'SSSphere__disclosure',
     305206: 'SSSplash__disclosure',
     902031: 'SSSSS_CopyToExtraColor',
     84771: 'SSSSS_HorizontalBlur',
     203704: 'SSSSS_Lighting',
     69274: 'SSSSS_VerticalBlur',
     1033842: 'SSSystemCopy',
     294798: 'SSTranslucency__disclosure',
     724040: 'SSVertexPosition__disclosure',
     533367: 'SSVfxNormal__disclosure',
     881186: 'SSVolumeBlendDepth',
     706952: 'SSVRLinear',
     343179: 'SSVRPoint',
     520403: 'SSVRWrapPoint',
     288049: 'SSWater',
     492253: 'SSWaterCustom',
     606312: 'SSWaterCustomWrap',
     737330: 'SSWaterWave',
     754653: 'SSWaterWaveWrap',
     343398: 'SSWaterWrap',
     462851: 'SSWrapLinear',
     653303: 'gColorPickTexture',
     367164: 'gDeformedVB',
     146702: 'gDestAccum',
     65699: 'gDestNormal',
     587581: 'gDestVB',
     362131: 'gE#2',
     378383: 'gE#hE2',
     856900: 'gE#W',
     905540: 'gEmitterBinary',
     357584: 'gEmitterData',
     829555: 'gEmitterDataUAV',
     790901: 'gEmitterRange',
     426885: 'gEmitterRangeUAV',
     392191: 'gEmitters',
     617564: 'gEmitterUsedIndices',
     238248: 'gErrorUnitCount',
     233560: 'gErrorUnitData',
     367786: 'gG^)',
     592829: 'gGpuLogUAV',
     686541: 'gHotLights',
     607541: 'gHotLightsSRV',
     542235: 'gImpactIvyFloorPointBuffer',
     896262: 'gImpactIvyFloorPointBufferPF',
     742917: 'gIndirectArgs',
     362313: 'gIndirectArgsUAV',
     737360: 'giNM',
     835534: 'gInstanceBuffer',
     819108: 'gInstanceBufferMaterial',
     241840: 'gInstanceBufferPF',
     638310: 'gInstanceIndexBuffer',
     955719: 'gjDI5s',
     97357: 'gJointMatrixBuffer',
     29535: 'gJointMatrixBufferPF',
     532819: 'gl/5',
     455274: 'gLightList',
     567153: 'gLightParameters',
     146027: 'gLuminanceBuffer',
     8816: 'gLuminanceBufferSRV',
     23615: 'gn',
     441135: 'gNL4',
     635759: 'gnYO',
     744671: 'gOriginalVB',
     891104: 'gP{I',
     1001792: 'gParticleBinary',
     369102: 'gParticleBinaryUAV',
     190534: 'gParticleData',
     1036018: 'gParticleDataUAV',
     211484: 'gParticleUnusedIndices',
     435068: 'gParticleUnusedIndicesUAV',
     1001468: 'gPickCount',
     738597: 'gPickData',
     1005431: 'gPlantOnSurfaceIndexBuffer',
     946448: 'gPlantOnSurfaceMatrixBuffer',
     286932: 'gPlantOnSurfaceMatrixBufferPF',
     1022092: 'gPrimCsInHeader',
     788731: 'gPrimCsInMaterials',
     805240: 'gPrimCsInVertex',
     435620: 'gPrimCsOutIndex',
     578589: 'gPrimCsOutVertex',
     13421: 'gPrimCsTemporal',
     914865: 'gPrimIndex',
     578503: 'gPrimTemporal',
     846758: 'gPrimVertex',
     642022: 'gq_w\\!',
     795688: 'gRedirect',
     229596: 'gRWRangeCheck',
     979007: 'gRWTexture',
     559140: 'gSortBuffer',
     1015069: 'gSpeedTreeBoundingBuffer',
     86077: 'gSpeedTreeCsLocalWindIndexBuffer',
     330594: 'gSpeedTreeGlobalWindBuffer',
     865113: 'gSpeedTreeGlobalWindBufferPF',
     148661: 'gSpeedTreeLocalWindBuffer',
     463495: 'gSpeedTreeLocalWindBufferPF',
     521999: 'gSpeedTreeLocalWindIndexBuffer',
     384454: 'gSpeedTreeLocalWindIndexBufferPF',
     231588: 'gSpeedTreeVertexBuffer',
     33748: 'gSrcAccum',
     73019: 'gSrcIB',
     725925: 'gSrcVB',
     865003: 'gStarInfoBuffer',
     1032650: 'gt?5',
     174484: 'gTileLightParam',
     114293: 'gTileLightParamSRV',
     1015074: 'gToneMappingHistogramSRV',
     868354: 'gToneMappingHistogramUAV',
     966649: 'gTubeLights',
     293251: 'gVZA',
     416670: 'gWavePointBuffer',
     469662: 'CB_BGTexture',
     398199: 'CB_CombinedFilter',
     780802: 'CB_CombinedFilter_ColorCorrect',
     590898: 'CB_CombinedFilter_ImagePlane',
     840276: 'CB_PlantOnSurface',
     90189: 'CB_TemporalAA',
     814541: 'CB_TemporalAA2',
     836737: 'CBAmbientOccluder',
     96627: 'CBAmbientOcclusion',
     1002683: 'CBAtmosphere',
     271514: 'CBBitonicSort',
     934144: 'CBBloom',
     928438: 'CBBloomSample',
     667092: 'CBBokehComposite',
     640563: 'CBColorCorrect',
     878207: 'CBColorCorrectCube',
     56328: 'CBColorCorrectToneCurve',
     873685: 'CBComputeSkinning',
     157772: 'CBConstant',
     509733: 'CBConstantHaltonSequence',
     667041: 'CBCSClear',
     797762: 'CBCubeBlend',
     829413: 'CBCubeCopy',
     424892: 'CBDebug',
     532310: 'CBDecal',
     10047: 'CBDecalCommon',
     255968: 'CBDepthColor',
     435226: 'CBDevelopColorPick',
     453289: 'CBDevelopFlags',
     1040353: 'CBDOFFilter',
     492950: 'CBErrorUnit',
     96832: 'CBFilter',
     844138: 'CBFilter2',
     509996: 'CBFog',
     489226: 'CBFXAAParam',
     549639: 'CBGaussian',
     175675: 'CBGlobalIllumination',
     203408: 'CBGodRaysConfiguration',
     272231: 'CBGodRaysFilter',
     128984: 'CBGodRaysIterator',
     578645: 'CBGUIDevelop',
     457230: 'CBGUIDistanceField',
     664072: 'CBGUIGBuffer',
     27312: 'CBGUIGlobal',
     1024113: 'CBGUIIcon',
     167410: 'CBGUINoiseAndFade',
     146335: 'CBHazeFilter',
     111354: 'CBHeightToNormal',
     126015: 'CBHermiteCurve',
     319294: 'CBHermiteCurveRGB',
     593689: 'CBImagePlane',
     176207: 'CBImagePlane2',
     190442: 'CBInstancing',
     316797: 'CBLGTPRBDebug',
     810033: 'CBLGTPRBGen',
     614636: 'CBLight',
     427394: 'CBLightParameters',
     235161: 'CBLightProbes',
     686716: 'CBLightShaft',
     741186: 'CBLightShaft_LightParam',
     583907: 'CBLuminance',
     32290: 'CBLuminanceDebugDisp',
     667076: 'CBLUTBlending',
     676677: 'CBLUTMaking',
     730930: 'CBMaterialCommon__disclosure',
     960186: 'CBMaterialDebug',
     160729: 'CBMhDecal',
     912056: 'CBMhDecalSM',
     1716: 'CBMhEmissiveFog__disclosure',
     1032420: 'CBMhMaterial_EM105_EVCLocal__disclosure',
     20549: 'CBMhMaterialBurnLocal__disclosure',
     452588: 'CBMhMaterialEC021Local__disclosure',
     733391: 'CBMhMaterialEM002Local__disclosure',
     1011189: 'CBMhMaterialEM011Local__disclosure',
     232123: 'CBMhMaterialEM024Local__disclosure',
     352608: 'CBMhMaterialEM036Local__disclosure',
     1016232: 'CBMhMaterialEM044Local__disclosure',
     548874: 'CBMhMaterialEM100Local__disclosure',
     789385: 'CBMhMaterialEM102Local__disclosure',
     965992: 'CBMhMaterialEM103Local__disclosure',
     131565: 'CBMhMaterialEM105Local__disclosure',
     318607: 'CBMhMaterialEM106Local__disclosure',
     751847: 'CBMhMaterialEM109Local__disclosure',
     546483: 'CBMhMaterialEM111Local__disclosure',
     33205: 'CBMhMaterialEM115Local__disclosure',
     322102: 'CBMhMaterialEM117Local__disclosure',
     740958: 'CBMhMaterialEM118Local__disclosure',
     714457: 'CBMhMaterialEMGlobal',
     729321: 'CBMhMaterialEMLocal__disclosure',
     589281: 'CBMhMaterialEMSLocal__disclosure',
     234467: 'CBMhMaterialFakeEyeLocal__disclosure',
     968274: 'CBMhMaterialFakeInnerEmitLocal__disclosure',
     660342: 'CBMhMaterialFakeLensLocal__disclosure',
     11637: 'CBMhMaterialFakeRefractionLocal__disclosure',
     843534: 'CBMhMaterialFakeSphereLocal__disclosure',
     145869: 'CBMhMaterialFlagWaveLocal__disclosure',
     806179: 'CBMhMaterialFlowDirLocal__disclosure',
     396463: 'CBMhMaterialFlowLavaLocal__disclosure',
     192097: 'CBMhMaterialFurLocal__disclosure',
     1022524: 'CBMhMaterialGlobal',
     98070: 'CBMhMaterialIvyFloor',
     847194: 'CBMhMaterialIvyFloorLocal__disclosure',
     726308: 'CBMhMaterialLandscapeFlowLocal__disclosure',
     474713: 'CBMhMaterialLandscapeLocal__disclosure',
     934631: 'CBMhMaterialNikuLocal__disclosure',
     1008871: 'CBMhMaterialNPCEditFaceLocal__disclosure',
     718142: 'CBMhMaterialNPCEyeLocal__disclosure',
     411529: 'CBMhMaterialNPCFaceLocal__disclosure',
     67121: 'CBMhMaterialNPCHairLocal__disclosure',
     37300: 'CBMhMaterialNPCLocal__disclosure',
     399972: 'CBMhMaterialNPCSkinLocal__disclosure',
     827466: 'CBMhMaterialPLEditFaceLocal__disclosure',
     840222: 'CBMhMaterialPLEyeLocal__disclosure',
     843998: 'CBMhMaterialPLHairLocal__disclosure',
     73912: 'CBMhMaterialPLLocal__disclosure',
     785547: 'CBMhMaterialPLSkinLocal__disclosure',
     854651: 'CBMhMaterialScrWaterLocal__disclosure',
     153016: 'CBMhMaterialSimpleLocal__disclosure',
     153850: 'CBMhMaterialSpeedTreeStdBlendLocal__disclosure',
     794239: 'CBMhMaterialSpeedTreeStdLocal__disclosure',
     28167: 'CBMhMaterialStdBlendLocal__disclosure',
     259486: 'CBMhMaterialStdLocal__disclosure',
     482324: 'CBMhMaterialUberLocal__disclosure',
     449186: 'CBMhMaterialVfxDebufBodyLocal__disclosure',
     861155: 'CBMhMaterialVfxDispWaveLocal__disclosure',
     935451: 'CBMhMaterialVfxDistDispLocal__disclosure',
     462001: 'CBMhMaterialVfxDistDispWLocal__disclosure',
     1011205: 'CBMhMaterialVfxFakeInnerLocal__disclosure',
     512095: 'CBMhMaterialVfxFloodLocal__disclosure',
     997910: 'CBMhMaterialVfxSandFallLocal__disclosure',
     49352: 'CBMhMaterialVfxTornadoLocal__disclosure',
     506681: 'CBMhMaterialVfxWaterLocal__disclosure',
     613132: 'CBMhMaterialVfxWave',
     559268: 'CBMhMaterialVfxWaveLocal__disclosure',
     610271: 'CBMhSky2GBuffer',
     289506: 'CBMhSky2PS',
     546332: 'CBMhSky2Sun',
     970084: 'CBMhSky2VS',
     263816: 'CBMhSkyGBuffer',
     583164: 'CBMhSkyLpPS',
     475985: 'CBMhSkyPS',
     910551: 'CBMhSkyVS',
     413378: 'CBModel',
     713589: 'CBMotionBlur',
     548865: 'CBMotionBlurReconstruction',
     755974: 'CBNewDOFFilter',
     741180: 'CBNewDOFFilter2',
     39274: 'CBNormalMerge',
     360306: 'CBNormalRecalc',
     1025284: 'CBOutline',
     591722: 'CBPartialColor',
     610830: 'CBPick',
     514610: 'CBPrimCopyState',
     419051: 'CBPrimGpuSystem',
     703926: 'CBPrimitive',
     610575: 'CBPrimitiveDebug',
     1015931: 'CBPrimitiveEx',
     583157: 'CBPrimitiveMetaDataOcclusion',
     6620: 'CBPrimitivePick',
     863014: 'CBPrimMaterialOffset',
     307726: 'CBPrimSystem',
     810415: 'CBPrimVertexOffset',
     192448: 'CBRadialBlurFilter',
     1450: 'CBRadialBlurFunction',
     465293: 'CBRenderFrame',
     540722: 'CBResample',
     1020097: 'CBROPTest',
     607341: 'CBScreen',
     684568: 'CBSHDiffuse',
     610768: 'CBSky',
     985243: 'CBSpeedTree',
     617531: 'CBSpeedTreeCollision__disclosure',
     942682: 'CBSpeedTreeGlobalWind',
     811957: 'CBSpeedTreeGlobalWindPF',
     13828: 'CBSpeedTreeLocalWind',
     173132: 'CBSpeedTreeLocalWindPF',
     37898: 'CBSpeedTreePrimitiveInfo',
     392360: 'CBSpeedTreeSystem',
     601539: 'CBSSLR',
     356197: 'CBSSSSS',
     547673: 'CBSSSSS_Profile',
     809094: 'CBStarrySky',
     537814: 'CBSystem',
     128844: 'CBSystemColor',
     730009: 'CBTest',
     153930: 'CBTestLight',
     172717: 'CBToneMapping',
     173374: 'CBToneMappingSdrSim',
     667173: 'CBTubeLight',
     447830: 'CBViewFrustum',
     758587: 'CBViewProjection',
     396804: 'CBVignetting',
     75752: 'CBVR_Debug',
     906928: 'CBVRCommon',
     747698: 'CBVRCompute',
     391676: 'CBVRFilter',
     590061: 'CBVRGaussian',
     102467: 'CBVRRecompute',
     376167: 'CBVRVolumeParams',
     10433: 'CBWater',
     886164: 'CBWaterCustom',
     736052: 'CBWaterDebug',
     590514: 'CBWaterMaterial',
     724554: 'CBWaterModel',
     576573: 'CBWaterPick',
     93743: 'CBWaterWave',
     675719: 'CBWaterWaveMaterial',
     934770: 'tAddNormalMap__disclosure',
     616043: 'tAddNormalMaskMap__disclosure',
     244712: 'tAerosolOpticalDepthMap',
     364362: 'tailColor',
     275936: 'tAlbedoBlendMap__disclosure',
     704025: 'tAlbedoBlendMapB__disclosure',
     331018: 'tAlbedoBlendMapG__disclosure',
     858307: 'tAlbedoBlendMapR__disclosure',
     67839: 'tAlbedoExtendMap__disclosure',
     338649: 'tAlbedoMap__disclosure',
     738756: 'tAlbedoOverMap__disclosure',
     69694: 'tAlbedoUniqueMap__disclosure',
     198920: 'tAlphaMap',
     222675: 'tAlphaMap__disclosure',
     879646: 'tAmbientOccluder',
     895298: 'tApproximateDepthMapOutput',
     763331: 'tAq>',
     299493: 'targetPos',
     10956: 'tAtmosphereOpticalDepthMap',
     44852: 'tBaseMap',
     787612: 'tBaseMaps',
     628817: 'tBlendMap',
     860112: 'tBlurTarget',
     946762: 'tbr`',
     862390: 'tBroadAreaShadowMap',
     671108: 'tCbr',
     620626: 'tCheckerDepth',
     78041: 'tChroma',
     1458: 'tColorFilter0',
     472356: 'tColorFilter1',
     697495: 'tColorFilterTable',
     756669: 'tColorMaskMap__disclosure',
     290771: 'tColorTargetUAV',
     335221: 'tCombinedTemporalMap',
     23103: 'tCubeMap',
     687225: 'tCubeMap__disclosure',
     746297: 'tDecalCubeMap',
     672872: 'tDecalFlowMap',
     571054: 'tDensityMap',
     854259: 'tDepthApproximateZMap',
     227254: 'tDepthMap',
     37412: 'tDepthMapOutput',
     953962: 'tDepthMipMapTarget0',
     638716: 'tDepthMipMapTarget1',
     61254: 'tDepthMipMapTarget2',
     516048: 'tDepthMipMapTarget3',
     1004006: 'tDepthTargetUAV',
     117478: 'tDetailEmissiveMap__disclosure',
     760213: 'tDetailMapA__disclosure',
     128347: 'tDetailMapB__disclosure',
     470238: 'tDetailMapC__disclosure',
     273030: 'tDetailMapD__disclosure',
     471195: 'tDetailNormalBlendMap__disclosure',
     920768: 'tDetailNormalMap__disclosure',
     938478: 'tDisplacementMap__disclosure',
     559004: 'tDitherMap',
     25075: 'tDOFMap',
     552042: 'tEffectDensity',
     57747: 'tEmissiveBlendMap__disclosure',
     485414: 'tEmissiveMap__disclosure',
     252609: 'tEmissiveMapB__disclosure',
     813522: 'tEmissiveMapG__disclosure',
     293915: 'tEmissiveMapR__disclosure',
     977710: 'texproj_mat',
     163238: 'texSampler',
     795183: 'tFaceMayuMap__disclosure',
     828416: 'tFaceNormalMap__disclosure',
     777441: 'tFacePaintMap__disclosure',
     602551: 'tFilterTempMap1',
     24589: 'tFilterTempMap2',
     406453: 'tFinalLuminance',
     1036429: 'tFinalTransmittance',
     593369: 'tFlowMap__disclosure',
     976467: 'tFogTable',
     946436: 'tFogVolumeMap',
     914487: 'tFullSSShadow',
     934456: 'tFurMap__disclosure',
     1036712: 'tFurMaskMap__disclosure',
     67676: 'tFurNormalMap__disclosure',
     312167: 'tFurVelocityMap__disclosure',
     77130: 'tFxMap__disclosure',
     764091: 'tG b',
     606847: 'tGBuffer0',
     946921: 'tGBuffer1',
     467795: 'tGBuffer2',
     5061: 'tGBuffer3',
     296550: 'tGBuffer4',
     243440: 'tGBuffer5',
     687186: 'tGBufferDepthMap',
     425006: 'tGI_IrradianceMapUAV',
     955297: 'tGlobalCubeOnly',
     198567: 'tGUIAlphaMap',
     376460: 'tGUIBaseMap',
     628478: 'tGUIBlendMap',
     991475: 'tGUIEdgeMap',
     933687: 'tGUIEmissiveMap',
     708877: 'tGUIFadeMap',
     27905: 'tGUINoiseMap',
     931863: 'tGUINormalMap',
     496931: 'tHeight',
     505201: 'tHeightMap',
     29520: 'thickness',
     797013: 'tHistory',
     447233: 'tHmL',
     817150: 'tHoldout',
     655093: 'tHoldoutDepth',
     514178: 'tHTileTexture',
     696827: 'tHTileTextureUAV',
     710123: 'tI*\t',
     179728: 'tileDepth',
     828415: 'tileLighting',
     638425: 'tileMultiList',
     197715: 'tileSingleList',
     1032014: 'tileUnused',
     134769: 'tiling',
     392005: 'tInput',
     460514: 'tIRMInputCube',
     148946: 'tIRMInputCube2',
     410924: 'tJ#l',
     71878: 'tK[Y',
     528571: 'tLightProbesShadowMap',
     83959: 'tLuma',
     179432: 'tLuminance',
     1034700: 'tLuminanceRecompute',
     367461: 'tLUT3DBlendMap',
     889177: 'tLUT3DMap0',
     696783: 'tLUT3DMap1',
     258165: 'tLUT3DMap2',
     607588: 'tMaskMap__disclosure',
     74303: 'tMaterialBlendMap__disclosure',
     175818: 'tMipFogMain',
     828487: 'tMipFogTable',
     591308: 'tMipFogTarget',
     921601: 'tMipMapOutput',
     274277: 'tMipMapTarget0',
     204787: 'tMipMapTarget1',
     675401: 'tMipMapTarget2',
     884447: 'tMipMapTarget3',
     227768: 'tNeighborMax',
     433169: 'tNormalBlendMap__disclosure',
     474682: 'tNormalBlendMapB__disclosure',
     560425: 'tNormalBlendMapG__disclosure',
     39136: 'tNormalBlendMapR__disclosure',
     795973: 'tNormalMap__disclosure',
     123937: 'tOcclusionMap',
     264621: 'tOpacityBlendMap__disclosure',
     351130: 'tOpacityMap__disclosure',
     889203: 'totalTime',
     102446: 'tOutlineObjectDepthMap',
     688188: 'tOutlineObjectMap',
     184635: 'tOutputCubeIRM',
     418103: 'tPaintKzMap__disclosure',
     609665: 'tPaintPbMap__disclosure',
     454415: 'tPanoramaMap__disclosure',
     522250: 'tPartsMaskMap__disclosure',
     351099: 'tPatternMap__disclosure',
     862204: 'tPrecomputedBRDFMap',
     934961: 'tPrecomputedMieOutput',
     144006: 'tPrecomputedMieSRV',
     302634: 'tPrecomputedRayleighOutput',
     676693: 'tPrecomputedRayleighSRV',
     765291: 'tPrevGBufferID',
     513797: 'tPrimAlbedoMap',
     370498: 'tPrimAlphaMask',
     419767: 'tPrimaryProjection',
     591179: 'tPrimaryShadow',
     879820: 'tPrimCubeMap',
     481574: 'tPrimDepthMap',
     598282: 'tPrimNormalMap',
     103898: 'tPrimSceneDepthMap',
     542001: 'tPrimSceneTex',
     826442: 'tPrimSceneTexDownSample',
     175942: 'tqaY`',
     953971: 'tr@u(',
     615614: 'transparency',
     138712: 'tReductionDepthMap',
     517994: 'tRefractionMap',
     430270: 'tRefractionMaskMap__disclosure',
     921268: 'tRMTBlendMap__disclosure',
     381391: 'tRMTBlendMapB__disclosure',
     680668: 'tRMTBlendMapG__disclosure',
     161557: 'tRMTBlendMapR__disclosure',
     606118: 'tRMTMap__disclosure',
     757911: 'tRWCbr',
     818645: 'tRWHeightMap',
     487804: 'tRWNeighborMax',
     12605: 'tRWTexture1',
     614535: 'tRWTexture2',
     938001: 'tRWTexture3',
     705970: 'tRWTexture4',
     103028: 'tRWTileMax',
     555884: 'tRWTileMaxHorizontal',
     661194: 'tryA>',
     199319: 'tSceneEnvMap',
     602939: 'tShadowMap',
     9747: 'tSHDiffuse',
     472645: 'tSkinMap__disclosure',
     508599: 'tSkyAlphaMap',
     972579: 'tSkyBaseMap',
     1023880: 'tSkyBlendAlphaMap',
     786892: 'tSkyBlendBaseMap',
     622261: 'tSkyBlendCloudMap0',
     937507: 'tSkyBlendCloudMap1',
     466841: 'tSkyBlendCloudMap2',
     12047: 'tSkyBlendCloudMap3',
     91561: 'tSkyBlendCloudRegionMap',
     1015291: 'tSkyBlendCloudsSideMap',
     379903: 'tSkyBlendCloudsTopMap0',
     195433: 'tSkyBlendCloudsTopMap1',
     873454: 'tSkyBlendMap',
     346510: 'tSkyBlendSunRegionMap',
     986069: 'tSkyCloudMap0',
     539459: 'tSkyCloudMap1',
     92921: 'tSkyCloudMap2',
     416367: 'tSkyCloudMap3',
     792950: 'tSkyCloudRegionMap',
     52264: 'tSkyCloudsSideMap',
     686636: 'tSkyCloudsTopMap0',
     871098: 'tSkyCloudsTopMap1',
     918376: 'tSkyCurveMap',
     668387: 'tSkyMap',
     446216: 'tSkyStarrySkyMap',
     568390: 'tSkySunAlphaMap',
     849409: 'tSkySunLightMaskMap',
     607826: 'tSkySunMap',
     941027: 'tSkySunMaskMap',
     195434: 'tSkySunRegionMap',
     135427: 'tSkyTempSunAlphaMap',
     401579: 'tSphereMap__disclosure',
     684885: 'tSplashMap__disclosure',
     827549: 'tSpotProjection',
     946411: 'tSrcMap',
     123904: 'tSrcMap2',
     809997: 'tSSAO',
     383477: 'tSSLR_BlurredMipMap',
     211308: 'tSSLR_ResolveMapUAV',
     725574: 'tSSLR_TemporalMapUAV',
     380236: 'tSSLRTraceMap',
     388105: 'tSSLRTraceMapUAV',
     903406: 'tSSShadowBlurMap',
     492581: 'tSSShadowMap',
     861376: 'tSSSSSMap',
     304675: 'tTemporalMap',
     21612: 'tTexture',
     569355: 'tTiledDeferredNumber',
     441281: 'tTiledDeferredOutput',
     396492: 'tTileMax',
     469836: 'tTileMaxHorizontal',
     978325: 'tToneCurveMap',
     849980: 'tTranslucencyMap__disclosure',
     332986: 'tTransmittance',
     44315: 'tTransmittanceRecompute',
     317745: 'tTransparentDensity',
     584879: 'tTubeLightTexture0',
     1039417: 'tTubeLightTexture1',
     429443: 'tTubeLightTexture2',
     113941: 'tTubeLightTexture3',
     936392: 'tTurbulenceVolume0',
     620894: 'tTurbulenceVolume1',
     10468: 'tTurbulenceVolume2',
     465010: 'tTurbulenceVolume3',
     232913: 'tTurbulenceVolume4',
     310599: 'tTurbulenceVolume5',
     912637: 'tTurbulenceVolume6',
     711787: 'tTurbulenceVolume7',
     911284: 'tUpsampledTexture',
     147968: 'tVertexNormalMap__disclosure',
     86866: 'tVertexPositionMap__disclosure',
     282661: 'tVertexTangentMap__disclosure',
     336665: 'tVfxNormalBlend',
     721504: 'tVoltexMap__disclosure',
     669351: 'tVolumeBlendDepthMap',
     67951: 'tVolumeFinalLuminance',
     786979: 'tVolumeFinalTransmittance',
     394215: 'tVolumeMap',
     207797: 'tVolumeTex0',
     269091: 'tVolumeTex1',
     871065: 'tVolumeTex2',
     686607: 'tVolumeTex3',
     646120: 'tw`:',
     818778: 'tWaterAlbedo',
     375566: 'tWaterBaseColor',
     61326: 'tWaterBaseDepth',
     62858: 'tWaterCaustics',
     433881: 'tWaterCausticsMap',
     656707: 'tWaterCubemap0',
     865749: 'tWaterCubemap1',
     715735: 'tWaterCubeMapA',
     244333: 'tWaterCubeMapB',
     29871: 'tWaterCustomAlbedoMap',
     669: 'tWaterCustomAlphaMap',
     153414: 'tWaterCustomCubeMap',
     1016244: 'tWaterCustomDetailNormalMap',
     957242: 'tWaterCustomEmissiveMap',
     225303: 'tWaterCustomFlowMap',
     950944: 'tWaterCustomNormalMap',
     105660: 'tWaterCustomProjectionNormalMap',
     411669: 'tWaterCustomRMTMap',
     489830: 'tWaterNoise',
     923015: 'tWaterNormal',
     973078: 'tWaterNormal2',
     194960: 'tWaterNormalA',
     763946: 'tWaterNormalB',
     287491: 'tWaterReflectionColor',
     79747: 'tWaterReflectionDepth',
     741448: 'tWaterRoughness',
     5996: 'tWaterWhitecap',
     255690: 'twxo',
     307233: 'typeAndDivision',
     891042: 'tza4W',
     771614: 'tzFG'}